<template>
  <q-drawer overlay show-if-above side="right">
    <q-card class="backgroundSecondary" flat>
      <q-card-section class="col q-col-gutter-y-sm">
        <div class="row justify-end">
          <q-btn class="buttonNeutral" icon="close" @click="$emit('close')"></q-btn>
        </div>
        <q-item-section>
          <q-item-label>
            <q-icon name="file_copy" />
            <div>Company</div>
          </q-item-label>
          <q-item-label>
            <h3>{{ modelValue?.name }}</h3>
          </q-item-label>
          <q-item-label>
            <q-btn icon="o_file_download" class="buttonNeutral"
                   :label="$t(`entity.action.download`)" />
          </q-item-label>
        </q-item-section>
        <!--      Download button -->
      </q-card-section>
      <q-list padding>
        <q-item>
          <q-item-section>Code</q-item-section>
          <q-item-section avatar>{{ modelValue?.code }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section> ID Type</q-item-section>
          <q-item-section avatar>{{ modelValue?.numberType }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>Legal ID Number</q-item-section>
          <q-item-section avatar>{{ modelValue?.number }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>Fiscal year closing month</q-item-section>
          <q-item-section avatar>{{ modelValue?.fiscalYearClosingMonth }}</q-item-section>
        </q-item>
      </q-list>


      <q-separator />
      <q-list>
        <q-expansion-item v-if="modelValue" dense-toggle expand-separator default-opened label="Address">
          <div class="q-pa-sm">
            <q-item>
              <q-item-section>Rue</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.streetName }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>Numéro</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.streetNumber }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>Code postal</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.postalCode }}</q-item-section>
            </q-item>


          </div>
        </q-expansion-item>
      </q-list>

      <q-separator />
      <q-list>
        <q-expansion-item v-if="modelValue" dense-toggle expand-separator label="History & Comments">
          <div class="q-pa-sm">
            <div>{{ modelValue?.history }}</div>
            <div>{{ modelValue?.comments }}</div>
          </div>
        </q-expansion-item>

      </q-list>

      <!--      <entity-meta :entity="modelValue" />-->
    </q-card>
  </q-drawer>
</template>

<script setup>
const props = defineProps({
  modelValue: Object
});


</script>
